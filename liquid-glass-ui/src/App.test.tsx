import React from 'react';
import { render, screen } from '@testing-library/react';
import App from './App';

test('renders Liquid Glass UI title', () => {
  render(<App />);
  const titleElement = screen.getByText(/Liquid Glass UI/i);
  expect(titleElement).toBeInTheDocument();
});

test('renders component sections', () => {
  render(<App />);
  expect(screen.getByRole('heading', { name: /按钮组件/i })).toBeInTheDocument();
  expect(screen.getByRole('heading', { name: /卡片组件/i })).toBeInTheDocument();
  expect(screen.getByRole('heading', { name: /模态框组件/i })).toBeInTheDocument();
});

test('renders theme toggle button', () => {
  render(<App />);
  const themeButton = screen.getByText(/切换到.*主题/i);
  expect(themeButton).toBeInTheDocument();
});
