import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import LiquidGlassButton from './LiquidGlassButton';

describe('LiquidGlassButton', () => {
  test('renders button with text', () => {
    render(<LiquidGlassButton>Click me</LiquidGlassButton>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  test('handles click events', () => {
    const handleClick = jest.fn();
    render(<LiquidGlassButton onClick={handleClick}>Click me</LiquidGlassButton>);
    
    fireEvent.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('applies correct CSS classes for variants', () => {
    const { rerender } = render(
      <LiquidGlassButton variant="glass" size="md" color="blue">
        Test
      </LiquidGlassButton>
    );
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('lg-button--glass');
    expect(button).toHaveClass('lg-button--md');
    expect(button).toHaveClass('lg-button--blue');

    rerender(
      <LiquidGlassButton variant="solid" size="lg" color="red">
        Test
      </LiquidGlassButton>
    );
    
    expect(button).toHaveClass('lg-button--solid');
    expect(button).toHaveClass('lg-button--lg');
    expect(button).toHaveClass('lg-button--red');
  });

  test('shows loading state', () => {
    render(<LiquidGlassButton loading>Loading</LiquidGlassButton>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('lg-button--loading');
    expect(button).toBeDisabled();
  });

  test('handles disabled state', () => {
    const handleClick = jest.fn();
    render(
      <LiquidGlassButton disabled onClick={handleClick}>
        Disabled
      </LiquidGlassButton>
    );
    
    const button = screen.getByRole('button');
    expect(button).toBeDisabled();
    expect(button).toHaveClass('lg-button--disabled');
    
    fireEvent.click(button);
    expect(handleClick).not.toHaveBeenCalled();
  });

  test('renders with icons', () => {
    const leftIcon = <span data-testid="left-icon">←</span>;
    const rightIcon = <span data-testid="right-icon">→</span>;
    
    render(
      <LiquidGlassButton leftIcon={leftIcon} rightIcon={rightIcon}>
        With Icons
      </LiquidGlassButton>
    );
    
    expect(screen.getByTestId('left-icon')).toBeInTheDocument();
    expect(screen.getByTestId('right-icon')).toBeInTheDocument();
  });

  test('applies full width class', () => {
    render(<LiquidGlassButton fullWidth>Full Width</LiquidGlassButton>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('lg-button--full-width');
  });

  test('forwards ref correctly', () => {
    const ref = React.createRef<HTMLButtonElement>();
    render(<LiquidGlassButton ref={ref}>Ref Test</LiquidGlassButton>);
    
    expect(ref.current).toBeInstanceOf(HTMLButtonElement);
  });
});
