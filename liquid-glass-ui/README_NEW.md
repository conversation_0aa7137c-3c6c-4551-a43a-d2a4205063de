# Liquid Glass UI

一个基于React 19的现代组件库，灵感来源于苹果iOS 26设计语言，实现半透明玻璃效果UI组件。

![Liquid Glass UI](https://img.shields.io/badge/React-19.1.0-blue)
![TypeScript](https://img.shields.io/badge/TypeScript-4.9.5-blue)
![License](https://img.shields.io/badge/License-MIT-green)

## ✨ 特性

- 🎨 **现代设计**: 基于苹果iOS 26设计语言的玻璃态效果
- ⚡ **React 19**: 使用最新的React特性和优化
- 🔧 **TypeScript**: 完整的类型定义支持
- 📱 **响应式**: 适配移动端和桌面端
- 🎯 **可访问性**: 内置ARIA支持和键盘导航
- 🌙 **主题支持**: 内置浅色/深色主题切换
- 🚀 **高性能**: 优化的CSS和最小化的重绘

## 🚀 快速开始

### 安装

```bash
npm install liquid-glass-ui
# 或
yarn add liquid-glass-ui
```

### 基本使用

```tsx
import React from 'react';
import { LiquidGlassButton, LiquidGlassCard, LiquidGlassModal } from 'liquid-glass-ui';

function App() {
  return (
    <div>
      <LiquidGlassButton variant="glass" color="blue">
        点击我
      </LiquidGlassButton>
      
      <LiquidGlassCard variant="glass" header={<h3>卡片标题</h3>}>
        <p>这是卡片内容</p>
      </LiquidGlassCard>
    </div>
  );
}
```

## 📦 组件

### LiquidGlassButton

半透明玻璃效果按钮组件，支持多种变体和状态。

```tsx
<LiquidGlassButton
  variant="glass"        // 'glass' | 'solid' | 'outline' | 'ghost'
  size="md"             // 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  color="blue"          // 'primary' | 'blue' | 'green' | 'red' | 等
  glassVariant="base"   // 'light' | 'base' | 'heavy'
  loading={false}
  disabled={false}
  onClick={() => console.log('clicked')}
>
  按钮文本
</LiquidGlassButton>
```

### LiquidGlassCard

半透明玻璃效果卡片容器组件。

```tsx
<LiquidGlassCard
  variant="glass"
  glassVariant="base"
  radius="lg"
  shadow="light"
  hoverable={true}
  header={<h3>卡片标题</h3>}
  footer={<button>操作按钮</button>}
>
  <p>卡片内容</p>
</LiquidGlassCard>
```

### LiquidGlassModal

半透明玻璃效果模态框组件。

```tsx
<LiquidGlassModal
  isOpen={isOpen}
  onClose={() => setIsOpen(false)}
  title="模态框标题"
  size="md"
  glassVariant="base"
  closeOnOverlayClick={true}
  footer={
    <>
      <button onClick={() => setIsOpen(false)}>取消</button>
      <button onClick={handleConfirm}>确认</button>
    </>
  }
>
  <p>模态框内容</p>
</LiquidGlassModal>
```

## 🎨 主题定制

### CSS变量

组件库使用CSS变量进行主题定制：

```css
:root {
  --lg-glass-blur: 20px;
  --lg-glass-bg-primary: rgba(255, 255, 255, 0.1);
  --lg-border-primary: rgba(255, 255, 255, 0.2);
  --lg-accent-blue: #007AFF;
  /* 更多变量... */
}
```

### 主题切换

```tsx
// 切换到深色主题
document.body.className = 'lg-dark';

// 切换到浅色主题
document.body.className = 'lg-light';
```

## 🛠 开发

### 本地开发

```bash
# 克隆仓库
git clone https://github.com/yourusername/liquid-glass-ui.git

# 安装依赖
cd liquid-glass-ui
npm install

# 启动开发服务器
npm start
```

### 构建

```bash
# 构建生产版本
npm run build

# 运行测试
npm test
```

## 📖 API文档

详细的API文档请参考组件文档。

## 🤝 贡献

欢迎贡献代码！

## 📄 许可证

MIT License

## 🔗 相关链接

- 在线演示: http://localhost:3000
- 设计灵感: 苹果iOS 26设计语言

---

由 ❤️ 制作，灵感来源于苹果设计
